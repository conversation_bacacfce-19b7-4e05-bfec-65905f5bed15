import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../services/theme_provider.dart';
import 'wifi_password_page.dart';

class WiFiSetupPage extends StatefulWidget {
  const WiFiSetupPage({super.key});

  @override
  State<WiFiSetupPage> createState() => _WiFiSetupPageState();
}

class _WiFiSetupPageState extends State<WiFiSetupPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  List<String> _wifiNetworks = [];
  bool _isLoadingNetworks = true;
  String? _selectedNetwork;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  // Pi configuration
  static const String _piBaseUrl = 'http://192.168.4.1:80';
  static const String _wifiEndpoint = '/wifi';

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // Start fade animation
    _fadeController.forward();

    // Fetch WiFi networks
    _fetchWiFiNetworks();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _fetchWiFiNetworks() async {
    try {
      print('Fetching WiFi networks from $_piBaseUrl$_wifiEndpoint');

      final response = await http.get(
        Uri.parse('$_piBaseUrl$_wifiEndpoint'),
        headers: {
          'Accept': 'application/json', // Request JSON response
          'Content-Type': 'application/json',
          'User-Agent': 'ImmyApp/1.0',
        },
      ).timeout(const Duration(seconds: 15));

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      if (response.statusCode == 200) {
        // First, try to parse as JSON
        try {
          final jsonData = json.decode(response.body);

          if (jsonData is Map && jsonData.containsKey('networks')) {
            // New JSON format from updated backend
            final networks = List<String>.from(jsonData['networks']);

            if (mounted) {
              setState(() {
                _wifiNetworks = networks;
                _isLoadingNetworks = false;
                _retryCount = 0;
              });

              if (networks.isEmpty && _retryCount < _maxRetries) {
                // No networks found, retry after a delay
                _showInfoMessage('No networks found. Retrying...');
                Future.delayed(const Duration(seconds: 2), () {
                  if (mounted) {
                    setState(() {
                      _retryCount++;
                    });
                    _fetchWiFiNetworks();
                  }
                });
              }
            }
          } else {
            throw Exception('Invalid JSON format');
          }
        } catch (jsonError) {
          // If JSON parsing fails, try HTML parsing as fallback
          print('JSON parsing failed, trying HTML parsing: $jsonError');
          final networks = _parseNetworksFromHTML(response.body);

          if (mounted) {
            setState(() {
              _wifiNetworks = networks;
              _isLoadingNetworks = false;
            });

            if (networks.isEmpty) {
              _showWarningMessage(
                  'No WiFi networks found. Make sure WiFi is enabled on the device.');
            }
          }
        }
      } else {
        throw Exception('Server responded with status: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching WiFi networks: $e');

      if (mounted) {
        setState(() {
          _isLoadingNetworks = false;
        });

        if (_retryCount < _maxRetries) {
          _showErrorMessage('Failed to load networks. Retrying...');
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              setState(() {
                _retryCount++;
                _isLoadingNetworks = true;
              });
              _fetchWiFiNetworks();
            }
          });
        } else {
          _showErrorMessage(
              'Failed to load WiFi networks after $_maxRetries attempts');

          // Add manual network entry option
          _showManualNetworkDialog();
        }
      }
    }
  }

  List<String> _parseNetworksFromHTML(String html) {
    // Fallback HTML parsing for backward compatibility
    final networks = <String>[];

    // Try to extract from select options
    final RegExp optionRegex = RegExp(r'<option value="([^"]+)"');
    final optionMatches = optionRegex.allMatches(html);

    for (final match in optionMatches) {
      final network = match.group(1);
      if (network != null && network.isNotEmpty) {
        networks.add(network);
      }
    }

    // If no options found, try to extract from list items
    if (networks.isEmpty) {
      final RegExp liRegex = RegExp(r'<li[^>]*>([^<]+)</li>');
      final liMatches = liRegex.allMatches(html);

      for (final match in liMatches) {
        final network = match.group(1)?.trim();
        if (network != null &&
            network.isNotEmpty &&
            !network.contains('No networks')) {
          networks.add(network);
        }
      }
    }

    return networks;
  }

  void _showManualNetworkDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDark = themeProvider.isDarkMode;
        final Color primaryColor =
            isDark ? const Color(0xFF6366F1) : const Color(0xFF8B5CF6);

        return AlertDialog(
          title: const Text('Enter Network Manually'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Unable to scan for networks. You can enter your WiFi network name manually:',
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: 'Network Name (SSID)',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.wifi),
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                final networkName = controller.text.trim();
                if (networkName.isNotEmpty) {
                  Navigator.of(context).pop();
                  setState(() {
                    _wifiNetworks = [networkName];
                    _selectedNetwork = networkName;
                  });
                  _showSuccessMessage('Network added manually');
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
              ),
              child: const Text(
                'Add Network',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFFDC2626),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFF16A34A),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFF3B82F6),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showWarningMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFFF59E0B),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _selectNetwork(String network) {
    setState(() {
      _selectedNetwork = network;
    });
  }

  void _proceedToPasswordPage() {
    if (_selectedNetwork != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) =>
              WiFiPasswordPage(networkName: _selectedNetwork!),
        ),
      );
    }
  }

  void _refreshNetworks() {
    setState(() {
      _isLoadingNetworks = true;
      _wifiNetworks = [];
      _selectedNetwork = null;
      _retryCount = 0;
    });
    _fetchWiFiNetworks();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.isDarkMode;
    final Color primaryColor =
        isDark ? const Color(0xFF6366F1) : const Color(0xFF8B5CF6);
    final Color secondaryColor =
        isDark ? const Color(0xFF8B5CF6) : const Color(0xFF6366F1);
    final Color backgroundColor =
        isDark ? const Color(0xFF18181B) : Colors.white;
    final Color cardColor = isDark ? const Color(0xFF27272A) : Colors.white;
    final Color textColor = isDark ? Colors.white : Colors.black87;
    final Color hintColor = isDark ? Colors.grey[400]! : Colors.grey[600]!;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: primaryColor,
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                'IA',
                style: TextStyle(
                  color: primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'WiFi Setup',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _refreshNetworks,
            tooltip: 'Refresh Networks',
          ),
          IconButton(
            icon: Icon(isDark ? Icons.dark_mode : Icons.light_mode,
                color: Colors.white),
            onPressed: () => themeProvider.toggleTheme(),
            tooltip: isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [
                    const Color(0xFF18181B),
                    const Color(0xFF27272A),
                    const Color(0xFF18181B)
                  ]
                : [
                    const Color(0xFF8B5CF6),
                    const Color(0xFF7C3AED),
                    Colors.white
                  ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),

                  // Animated Immy Bear Icon
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [primaryColor, secondaryColor],
                            ),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: primaryColor.withOpacity(0.4),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: Image.asset(
                              'assets/immy_BrainyBear.png',
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(
                                  child: Text(
                                    'IB',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 40,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      );
                    },
                  ),

                  const SizedBox(height: 30),

                  // Welcome Card
                  Card(
                    elevation: 8,
                    shadowColor: Colors.black.withOpacity(0.2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    color: cardColor,
                    child: Padding(
                      padding: EdgeInsets.all(isSmallScreen ? 20.0 : 24.0),
                      child: Column(
                        children: [
                          ShaderMask(
                            shaderCallback: (bounds) => LinearGradient(
                              colors: [primaryColor, secondaryColor],
                            ).createShader(bounds),
                            child: Text(
                              'Connect Immy to WiFi!',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Choose a WiFi network for your Immy device to connect to.',
                            style: TextStyle(
                              fontSize: isSmallScreen ? 14 : 16,
                              color: hintColor,
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // WiFi Networks Card
                  Card(
                    elevation: 8,
                    shadowColor: Colors.black.withOpacity(0.2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    color: cardColor,
                    child: Padding(
                      padding: EdgeInsets.all(isSmallScreen ? 20.0 : 24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.wifi, color: primaryColor, size: 24),
                              const SizedBox(width: 8),
                              Text(
                                'Available Networks',
                                style: TextStyle(
                                  color: textColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              const Spacer(),
                              // Manual add button
                              IconButton(
                                onPressed: _showManualNetworkDialog,
                                icon: Icon(Icons.add_circle_outline,
                                    color: primaryColor),
                                tooltip: 'Add network manually',
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (_isLoadingNetworks)
                            Center(
                              child: Column(
                                children: [
                                  CircularProgressIndicator(
                                      color: primaryColor),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Scanning for networks...',
                                    style: TextStyle(color: hintColor),
                                  ),
                                  if (_retryCount > 0)
                                    Text(
                                      'Retry $_retryCount of $_maxRetries',
                                      style: TextStyle(
                                        color: hintColor,
                                        fontSize: 12,
                                      ),
                                    ),
                                ],
                              ),
                            )
                          else if (_wifiNetworks.isEmpty)
                            Center(
                              child: Column(
                                children: [
                                  Icon(Icons.wifi_off,
                                      color: hintColor, size: 48),
                                  const SizedBox(height: 16),
                                  Text(
                                    'No networks found',
                                    style: TextStyle(color: hintColor),
                                  ),
                                  const SizedBox(height: 8),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      TextButton(
                                        onPressed: _refreshNetworks,
                                        child: Text(
                                          'Retry Scan',
                                          style: TextStyle(color: primaryColor),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      TextButton(
                                        onPressed: _showManualNetworkDialog,
                                        child: Text(
                                          'Enter Manually',
                                          style: TextStyle(color: primaryColor),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          else
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: _wifiNetworks.length,
                              separatorBuilder: (context, index) =>
                                  const SizedBox(height: 8),
                              itemBuilder: (context, index) {
                                final network = _wifiNetworks[index];
                                final isSelected = _selectedNetwork == network;

                                return InkWell(
                                  onTap: () => _selectNetwork(network),
                                  borderRadius: BorderRadius.circular(12),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16,
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: isSelected
                                            ? primaryColor
                                            : hintColor.withOpacity(0.3),
                                        width: isSelected ? 2 : 1,
                                      ),
                                      color: isSelected
                                          ? primaryColor.withOpacity(0.1)
                                          : Colors.transparent,
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.wifi,
                                          color: isSelected
                                              ? primaryColor
                                              : hintColor,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            network,
                                            style: TextStyle(
                                              color: isSelected
                                                  ? primaryColor
                                                  : textColor,
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                        if (isSelected)
                                          Icon(
                                            Icons.check_circle,
                                            color: primaryColor,
                                            size: 20,
                                          ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Connect Button
                  Container(
                    width: double.infinity,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: _selectedNetwork != null
                            ? [primaryColor, secondaryColor]
                            : [Colors.grey, Colors.grey.shade400],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: _selectedNetwork != null
                          ? [
                              BoxShadow(
                                color: primaryColor.withOpacity(0.4),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ]
                          : [],
                    ),
                    child: ElevatedButton(
                      onPressed: _selectedNetwork != null
                          ? _proceedToPasswordPage
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.arrow_forward,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            _selectedNetwork != null
                                ? 'Connect to $_selectedNetwork'
                                : 'Select a Network',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
