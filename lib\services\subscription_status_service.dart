import 'backend_api_service.dart';

/// Centralized service for managing subscription status and operations
/// This service ensures consistent subscription status handling across the app
class SubscriptionStatusService {
  
  /// Check if a user has an active subscription
  /// This method automatically updates expired subscriptions in the database
  static Future<bool> hasActiveSubscription(int userId) async {
    try {
      return await BackendApiService.hasActiveSubscription(userId);
    } catch (e) {
      print('Error checking subscription status: $e');
      return false;
    }
  }
  
  /// Get the next payment date for a user
  /// Returns null if no active subscription exists
  static Future<DateTime?> getNextPaymentDate(int userId) async {
    try {
      return await BackendApiService.getNextPaymentDate(userId);
    } catch (e) {
      print('Error getting next payment date: $e');
      return null;
    }
  }
  
  /// Get user subscriptions with automatic status updates
  /// This ensures expired subscriptions are marked as 'expired' in the database
  static Future<List<Map<String, dynamic>>> getUserSubscriptions(int userId) async {
    try {
      return await BackendApiService.getUserSubscriptions(userId);
    } catch (e) {
      print('Error getting user subscriptions: $e');
      return [];
    }
  }
  
  /// Get subscription status summary for a user
  /// Returns a comprehensive status object
  static Future<SubscriptionStatus> getSubscriptionStatus(int userId) async {
    try {
      final subscriptions = await getUserSubscriptions(userId);
      final hasActive = await hasActiveSubscription(userId);
      final nextPayment = await getNextPaymentDate(userId);
      
      // Find the most recent active subscription
      Map<String, dynamic>? activeSubscription;
      if (hasActive && subscriptions.isNotEmpty) {
        final activeSubscriptions = subscriptions.where((sub) => sub['status'] == 'active').toList();
        if (activeSubscriptions.isNotEmpty) {
          // Sort by end_date descending to get the latest one
          activeSubscriptions.sort((a, b) {
            final dateA = a['end_date'] is String ? DateTime.parse(a['end_date']) : a['end_date'] as DateTime;
            final dateB = b['end_date'] is String ? DateTime.parse(b['end_date']) : b['end_date'] as DateTime;
            return dateB.compareTo(dateA);
          });
          activeSubscription = activeSubscriptions.first;
        }
      }
      
      return SubscriptionStatus(
        hasActiveSubscription: hasActive,
        nextPaymentDate: nextPayment,
        activeSubscription: activeSubscription,
        allSubscriptions: subscriptions,
      );
    } catch (e) {
      print('Error getting subscription status: $e');
      return SubscriptionStatus(
        hasActiveSubscription: false,
        nextPaymentDate: null,
        activeSubscription: null,
        allSubscriptions: [],
      );
    }
  }
  
  /// Update expired subscriptions for a specific user
  /// This is called automatically by other methods, but can be called manually if needed
  static Future<void> updateExpiredSubscriptions(int userId) async {
    try {
      // This is handled internally by BackendApiService.getUserSubscriptions
      await BackendApiService.getUserSubscriptions(userId);
    } catch (e) {
      print('Error updating expired subscriptions: $e');
    }
  }
  
  /// Check if a subscription is expired based on its end date
  static bool isSubscriptionExpired(Map<String, dynamic> subscription) {
    try {
      final endDate = subscription['end_date'];
      final DateTime endDateTime;
      
      if (endDate is String) {
        endDateTime = DateTime.parse(endDate);
      } else if (endDate is DateTime) {
        endDateTime = endDate;
      } else {
        return true; // Consider it expired if we can't parse the date
      }
      
      return endDateTime.isBefore(DateTime.now());
    } catch (e) {
      print('Error checking if subscription is expired: $e');
      return true; // Consider it expired on error
    }
  }
  
  /// Get a user-friendly status message
  static String getStatusMessage(SubscriptionStatus status) {
    if (status.hasActiveSubscription && status.nextPaymentDate != null) {
      final daysUntilPayment = status.nextPaymentDate!.difference(DateTime.now()).inDays;
      if (daysUntilPayment > 0) {
        return 'Active subscription - next payment in $daysUntilPayment days';
      } else {
        return 'Active subscription - payment due soon';
      }
    } else if (status.allSubscriptions.isNotEmpty) {
      return 'Subscription expired - please renew to continue';
    } else {
      return 'No subscription - subscribe to access premium features';
    }
  }
}

/// Data class to hold comprehensive subscription status information
class SubscriptionStatus {
  final bool hasActiveSubscription;
  final DateTime? nextPaymentDate;
  final Map<String, dynamic>? activeSubscription;
  final List<Map<String, dynamic>> allSubscriptions;
  
  const SubscriptionStatus({
    required this.hasActiveSubscription,
    required this.nextPaymentDate,
    required this.activeSubscription,
    required this.allSubscriptions,
  });
  
  /// Get the subscription end date if available
  DateTime? get subscriptionEndDate {
    if (activeSubscription == null) return null;
    
    try {
      final endDate = activeSubscription!['end_date'];
      if (endDate is String) {
        return DateTime.parse(endDate);
      } else if (endDate is DateTime) {
        return endDate;
      }
    } catch (e) {
      print('Error parsing subscription end date: $e');
    }
    
    return null;
  }
  
  /// Get the subscription start date if available
  DateTime? get subscriptionStartDate {
    if (activeSubscription == null) return null;
    
    try {
      final startDate = activeSubscription!['start_date'];
      if (startDate is String) {
        return DateTime.parse(startDate);
      } else if (startDate is DateTime) {
        return startDate;
      }
    } catch (e) {
      print('Error parsing subscription start date: $e');
    }
    
    return null;
  }
  
  /// Get days remaining in subscription
  int get daysRemaining {
    if (!hasActiveSubscription || subscriptionEndDate == null) return 0;
    
    final remaining = subscriptionEndDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
  
  /// Check if subscription is expiring soon (within 3 days)
  bool get isExpiringSoon {
    return hasActiveSubscription && daysRemaining <= 3 && daysRemaining > 0;
  }
}
