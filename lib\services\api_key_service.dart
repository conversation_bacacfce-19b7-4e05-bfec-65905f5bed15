import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'backend_api_service.dart';
import 'users_auth_service.dart';

/// Service for managing API keys - storage, validation, and Pi communication
class ApiKeyService {
  static const String _apiKeyKey = 'user_api_key';
  static const String _apiKeyValidatedKey = 'api_key_validated';
  static const String _piIpAddress = '***********';
  static const int _piPort = 80;

  /// Validate API key format
  static bool isValidApiKey(String apiKey) {
    // Simply check if the API key is not empty after trimming
    return apiKey.trim().isNotEmpty;
  }

  /// Save API key to local storage and database
  static Future<bool> saveApiKey(String apiKey) async {
    try {
      if (!isValidApiKey(apiKey)) {
        if (kDebugMode) {
          print('❌ Invalid API key format');
        }
        return false;
      }

      final trimmedApiKey = apiKey.trim();

      // 1. Save to SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_apiKeyKey, trimmedApiKey);
      await prefs.setBool(_apiKeyValidatedKey, true);

      if (kDebugMode) {
        print('✅ API Key saved to SharedPreferences');
      }

      // 2. Save to database for the current user
      await _saveApiKeyToDatabase(trimmedApiKey);

      if (kDebugMode) {
        print(
            '✅ API Key saved successfully to both SharedPreferences and database');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving API Key: $e');
      }
      return false;
    }
  }

  /// Get API key from local storage
  static Future<String?> getApiKey() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_apiKeyKey);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting API Key: $e');
      }
      return null;
    }
  }

  /// Check if API key is validated
  static Future<bool> isApiKeyValidated() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_apiKeyValidatedKey) ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking API Key validation: $e');
      }
      return false;
    }
  }

  /// Save API key to database for the current user
  static Future<void> _saveApiKeyToDatabase(String apiKey) async {
    try {
      // Get current user
      final authService = AuthService();
      final user = await authService.getCurrentUser();

      if (user == null) {
        if (kDebugMode) {
          print('⚠️ No user logged in, skipping database save');
        }
        return;
      }

      // Check if user has a serial number assigned
      final existingSerial =
          await BackendApiService.getSerialWithApiKeyByUserId(user.id);

      if (existingSerial != null) {
        // Update existing serial's API key
        await BackendApiService.updateSerialApiKey(
            existingSerial['id'], apiKey);
        if (kDebugMode) {
          print(
              '✅ Updated API key in database for existing serial ID: ${existingSerial['id']}');
        }
      } else {
        // User doesn't have a serial assigned yet, create one and assign the API key
        if (kDebugMode) {
          print(
              'ℹ️ User has no serial assigned, creating one and assigning API key...');
          print('🔍 User ID: ${user.id}, API Key: $apiKey');
        }

        // Generate a unique serial number for the user
        final serialNumber =
            'SN-${user.id}-${DateTime.now().millisecondsSinceEpoch}';
        if (kDebugMode) {
          print('🔍 Generated serial number: $serialNumber');
        }

        // Create new serial number
        final newSerial =
            await BackendApiService.createSerialNumber(serialNumber);
        final serialId = newSerial['id'];
        if (kDebugMode) {
          print('✅ Created new serial with ID: $serialId');
        }

        // Assign the serial and API key to the user
        await BackendApiService.assignSerialAndApiKeyToUser(serialId, user.id,
            apiKey: apiKey);

        if (kDebugMode) {
          print(
              '✅ Assigned serial ID: $serialId and API key to user ID: ${user.id}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving API key to database: $e');
      }
      // Don't throw the error, just log it since SharedPreferences save was successful
    }
  }

  /// Clear API key from local storage
  static Future<void> clearApiKey() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_apiKeyKey);
      await prefs.remove(_apiKeyValidatedKey);

      if (kDebugMode) {
        print('✅ API Key cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing API Key: $e');
      }
    }
  }

  /// Send API key and user credentials to Pi
  static Future<Map<String, dynamic>> sendCredentialsToPi({
    required String apiKey,
    required String username,
    String? userEmail,
    int? userId,
  }) async {
    try {
      if (!isValidApiKey(apiKey)) {
        return {
          'success': false,
          'error': 'Invalid API key format',
        };
      }

      // Prepare credentials data
      final credentialsData = {
        'username': username,
        'api_key': apiKey,
        'timestamp': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
      };

      // Add optional data if provided
      if (userEmail != null) {
        credentialsData['email'] = userEmail;
      }
      if (userId != null) {
        credentialsData['user_id'] = userId.toString();
      }

      if (kDebugMode) {
        print(
            '🚀 Sending credentials to Pi: ${credentialsData['username']} / ${_maskApiKey(apiKey)}');
      }

      // Send to Pi
      final response = await http
          .post(
            Uri.parse('http://$_piIpAddress:$_piPort/save_creds'),
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ImmyApp/1.0',
            },
            body: jsonEncode(credentialsData),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        if (kDebugMode) {
          print('✅ Credentials sent to Pi successfully');
        }

        return {
          'success': true,
          'message': 'Credentials sent to Immy successfully!',
          'response': response.body,
        };
      } else {
        if (kDebugMode) {
          print('❌ Pi responded with status: ${response.statusCode}');
        }

        return {
          'success': false,
          'error': 'Pi responded with status: ${response.statusCode}',
          'details': response.body,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending credentials to Pi: $e');
      }

      return {
        'success': false,
        'error': 'Failed to connect to Immy: ${e.toString()}',
      };
    }
  }

  /// Send API key to Pi with current user data
  static Future<Map<String, dynamic>> sendCurrentUserCredentialsToPi(
      String apiKey) async {
    try {
      final authService = AuthService();
      final user = await authService.getCurrentUser();

      if (user == null) {
        return {
          'success': false,
          'error': 'No user logged in. Please log in first.',
        };
      }

      return await sendCredentialsToPi(
        apiKey: apiKey,
        username: user.name,
        userEmail: user.email,
        userId: user.id,
      );
    } catch (e) {
      return {
        'success': false,
        'error': 'Error getting user data: ${e.toString()}',
      };
    }
  }

  /// Test connection to Pi
  static Future<bool> testPiConnection() async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$_piIpAddress:$_piPort/'),
          )
          .timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Pi connection test failed: $e');
      }
      return false;
    }
  }

  /// Mask API key for logging (show first 4 and last 4 characters)
  static String _maskApiKey(String apiKey) {
    if (apiKey.length <= 8) {
      return '*' * apiKey.length;
    }
    return '${apiKey.substring(0, 4)}${'*' * (apiKey.length - 8)}${apiKey.substring(apiKey.length - 4)}';
  }

  /// Validate API key with external service (if applicable)
  static Future<Map<String, dynamic>> validateApiKeyWithService(
      String apiKey) async {
    try {
      // This is a placeholder for actual API validation
      // You would implement actual validation based on your API service

      if (!isValidApiKey(apiKey)) {
        return {
          'valid': false,
          'error': 'Please enter an API key',
        };
      }

      // For now, we'll just check if it's not empty
      return {
        'valid': true,
        'message': 'API key accepted',
      };
    } catch (e) {
      return {
        'valid': false,
        'error': 'Validation failed: ${e.toString()}',
      };
    }
  }

  /// Get masked API key for display
  static Future<String?> getMaskedApiKey() async {
    final apiKey = await getApiKey();
    if (apiKey == null) return null;
    return _maskApiKey(apiKey);
  }

  /// Load API key from database for the current user and sync with SharedPreferences
  static Future<void> loadApiKeyFromDatabase() async {
    try {
      // Get current user
      final authService = AuthService();
      final user = await authService.getCurrentUser();

      if (user == null) {
        if (kDebugMode) {
          print('⚠️ No user logged in, cannot load API key from database');
        }
        return;
      }

      // Get API key from database
      final apiKey = await BackendApiService.getUserApiKeyByEmail(user.email);

      if (apiKey != null && apiKey.isNotEmpty) {
        // Save to SharedPreferences (without triggering database save again)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_apiKeyKey, apiKey);
        await prefs.setBool(_apiKeyValidatedKey, true);

        if (kDebugMode) {
          print(
              '✅ API key loaded from database and synced to SharedPreferences');
        }
      } else {
        if (kDebugMode) {
          print('ℹ️ No API key found in database for user: ${user.email}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading API key from database: $e');
      }
    }
  }
}
