# Subscription Status Management Improvements

## Problem Summary
The user reported that every time they opened the payment page, it appeared as if they had just paid today, even though they had paid on September 1st. This was caused by several issues in the subscription date handling system.

## Root Causes Identified

### 1. **Incorrect Next Payment Date Calculation**
- **Issue**: The payment page calculated "next payment" by taking the first payment's `created_at` date and adding 30 days every time the page loaded
- **Result**: Made it appear as if the user paid today (30 days ago from "next payment")
- **Location**: `lib/screens/payments_page.dart` line 1063

### 2. **Subscription Recreation with Today's Date**
- **Issue**: When a payment existed but no active subscription was found, the system created a new subscription with `endDate = DateTime.now().add(Duration(days: 30))`
- **Result**: Reset subscription period to start from today instead of preserving original payment date
- **Location**: `lib/screens/payments_page.dart` line 472

### 3. **Webhook Subscription Updates Using Current Date**
- **Issue**: Webhook handlers updated existing subscriptions with `DateTime.now().add(Duration(days: 30))` instead of extending from current end date
- **Result**: Reset billing cycle to start from today on every webhook update
- **Location**: `lib/services/webhook_handler.dart` line 267

### 4. **No Automatic Expired Subscription Handling**
- **Issue**: Expired subscriptions remained marked as "active" in the database
- **Result**: Inconsistent subscription status and manual calculations instead of database-driven logic

## Solutions Implemented

### 1. **Fixed Next Payment Date Display**
```dart
// OLD: Calculated from payment date + 30 days every time
nextPaymentDate = _parseDateTime(_payments.first['created_at']).add(const Duration(days: 30));

// NEW: Uses actual subscription end_date from database
final activeSubscriptions = _subscriptions.where((sub) => sub['status'] == 'active').toList();
activeSubscriptions.sort((a, b) => _parseDateTime(a['end_date']).compareTo(_parseDateTime(b['end_date'])));
nextPaymentDate = _parseDateTime(activeSubscriptions.first['end_date']);
```

### 2. **Fixed Subscription Recreation Logic**
```dart
// OLD: Used today's date
final endDate = DateTime.now().add(const Duration(days: 30));

// NEW: Uses original payment date
final paymentDate = _parseDateTime(payments.first['created_at']);
final endDate = paymentDate.add(const Duration(days: 30));
```

### 3. **Fixed Webhook Subscription Updates**
```dart
// OLD: Reset to today + 30 days
final endDate = DateTime.now().add(const Duration(days: 30));

// NEW: Extends from current end date
final currentEndDate = DateTime.parse(currentSubscription['end_date']);
final baseDate = currentEndDate.isAfter(DateTime.now()) ? currentEndDate : DateTime.now();
final newEndDate = baseDate.add(const Duration(days: 30));
```

### 4. **Added Automatic Expired Subscription Handling**
```dart
// NEW: Automatic status updates in getUserSubscriptions
static Future<void> _updateExpiredSubscriptions(int userId) async {
  await executeQuery(
    'UPDATE Subscriptions SET status = ? WHERE user_id = ? AND status = ? AND end_date <= NOW()',
    ['expired', userId, 'active']
  );
}
```

### 5. **Created Centralized Subscription Status Service**
- **New Service**: `SubscriptionStatusService` provides consistent subscription status management
- **Features**:
  - Automatic expired subscription updates
  - Centralized status checking
  - Comprehensive subscription status information
  - User-friendly status messages

## Database Schema Improvements

The subscription table properly stores:
```sql
CREATE TABLE Subscriptions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  serial_id INT NOT NULL,
  start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- Actual payment date
  end_date TIMESTAMP NOT NULL,                     -- When subscription expires
  status VARCHAR(50) DEFAULT 'active',             -- 'active', 'expired', 'cancelled'
  stripe_subscription_id VARCHAR(255),
  stripe_price_id VARCHAR(255)
);
```

## How It Works Now

### 1. **Payment Processing**
- When a user pays, subscription is created with correct start/end dates
- End date is calculated from payment date, not current date
- Status is set to 'active'

### 2. **Status Checking**
- `getUserSubscriptions()` automatically updates expired subscriptions to 'expired' status
- Payment page displays actual subscription end date as "next payment"
- No more manual date calculations

### 3. **Subscription Renewal**
- Webhooks extend subscription from current end date, not from today
- Preserves original billing cycle
- Proper subscription continuity

### 4. **User Experience**
- **Before**: "Next payment: [30 days from today]" (always appeared as if paid today)
- **After**: "Next payment: [actual subscription end date]" (shows real renewal date)

## Testing the Fix

To verify the improvements:

1. **Check existing subscriptions**: Users should see correct "Next payment" dates based on their actual subscription end dates
2. **Make a new payment**: Subscription should be created with proper dates based on payment time
3. **Check after subscription expires**: Status should automatically update to 'expired'
4. **Renew subscription**: Should extend from original end date, not reset to today

## Benefits

1. **Accurate Date Display**: Users see real subscription and payment dates
2. **Consistent Status**: Database-driven status management eliminates inconsistencies
3. **Proper Billing Cycles**: Subscription renewals maintain original billing schedule
4. **Automatic Maintenance**: Expired subscriptions are automatically updated
5. **Centralized Logic**: All subscription operations use consistent logic

The subscription system now properly stores and retrieves data from the database instead of calculating dates on-the-fly, ensuring users see accurate subscription information that reflects their actual payment history.
