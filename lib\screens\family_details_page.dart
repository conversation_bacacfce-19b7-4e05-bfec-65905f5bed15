import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../services/theme_provider.dart';

class FamilyDetailsPage extends StatefulWidget {
  const FamilyDetailsPage({super.key});

  @override
  State<FamilyDetailsPage> createState() => _FamilyDetailsPageState();
}

class _FamilyDetailsPageState extends State<FamilyDetailsPage>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final _userNameController = TextEditingController();
  final _fatherNameController = TextEditingController();
  final _motherNameController = TextEditingController();
  final _siblingsController = TextEditingController();

  bool _isSendingData = false;

  // Pi configuration
  static const String _piBaseUrl = 'http://192.168.4.1:80';
  static const String _userConfigEndpoint = '/';

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // Start fade animation
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    _userNameController.dispose();
    _fatherNameController.dispose();
    _motherNameController.dispose();
    _siblingsController.dispose();
    super.dispose();
  }

  Future<void> _sendFamilyDetails() async {
    // Check if all fields are empty for skip-like behavior
    if (_userNameController.text.trim().isEmpty &&
        _fatherNameController.text.trim().isEmpty &&
        _motherNameController.text.trim().isEmpty &&
        _siblingsController.text.trim().isEmpty) {
      _showInfoMessage(
          'No family details provided. Proceeding to WiFi setup...');
      _navigateToNextPage();
      return;
    }

    // Validate only if at least one field is filled
    bool hasAnyData = _userNameController.text.trim().isNotEmpty ||
        _fatherNameController.text.trim().isNotEmpty ||
        _motherNameController.text.trim().isNotEmpty ||
        _siblingsController.text.trim().isNotEmpty;

    if (hasAnyData) {
      // If any field has data, require at least the user name
      if (_userNameController.text.trim().isEmpty) {
        _showErrorMessage('Please enter your name or skip this step');
        return;
      }
    }

    setState(() {
      _isSendingData = true;
    });

    try {
      // Prepare family details data
      final familyData = {
        'user_name': _userNameController.text.trim(),
        'father_name': _fatherNameController.text.trim(),
        'mother_name': _motherNameController.text.trim(),
        'siblings': _siblingsController.text.trim(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Send family details to Pi
      final response = await http
          .post(
            Uri.parse('$_piBaseUrl$_userConfigEndpoint'),
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'ImmyApp/1.0',
            },
            body: json.encode(familyData),
          )
          .timeout(const Duration(seconds: 10));

      if (mounted) {
        if (response.statusCode == 200) {
          // Parse the response to check for success
          try {
            final responseData = json.decode(response.body);
            if (responseData['success'] == true || response.statusCode == 200) {
              _showSuccessMessage(responseData['message'] ??
                  'Family details sent successfully!');
              // Wait for success message to be visible before navigating
              Future.delayed(const Duration(seconds: 2), () {
                _navigateToNextPage();
              });
            } else {
              throw Exception(responseData['error'] ?? 'Unknown server error');
            }
          } catch (e) {
            // If response isn't JSON, consider it successful if status is 200
            _showSuccessMessage('Family details sent successfully!');
            Future.delayed(const Duration(seconds: 2), () {
              _navigateToNextPage();
            });
          }
        } else {
          // Try to parse error message from response
          try {
            final errorData = json.decode(response.body);
            throw Exception(errorData['error'] ??
                'Server responded with status: ${response.statusCode}');
          } catch (_) {
            throw Exception(
                'Server responded with status: ${response.statusCode}');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorMessage(
            'Failed to send family details: ${e.toString().replaceAll('Exception: ', '')}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSendingData = false;
        });
      }
    }
  }

  void _skipFamilyDetails() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDark = themeProvider.isDarkMode;
        final Color primaryColor =
            isDark ? const Color(0xFF6366F1) : const Color(0xFF8B5CF6);

        return AlertDialog(
          title: const Text('Skip Family Details?'),
          content: const Text(
            'You can always add this information later. Would you like to proceed to WiFi setup?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _showInfoMessage('Skipping family details...');
                _navigateToNextPage();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
              ),
              child: const Text(
                'Skip',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFF16A34A),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFFDC2626),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showInfoMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: const Color(0xFF16A34A),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _navigateToNextPage() {
    // Wait a moment for the message to be visible, then navigate
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        // Navigate to WiFi setup page
        Navigator.of(context).pushReplacementNamed('/wifi_setup');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDark = themeProvider.isDarkMode;
    final Color primaryColor =
        isDark ? const Color(0xFF6366F1) : const Color(0xFF8B5CF6);
    final Color secondaryColor =
        isDark ? const Color(0xFF8B5CF6) : const Color(0xFF6366F1);
    final Color backgroundColor =
        isDark ? const Color(0xFF18181B) : Colors.white;
    final Color cardColor = isDark ? const Color(0xFF27272A) : Colors.white;
    final Color textColor = isDark ? Colors.white : Colors.black87;
    final Color hintColor = isDark ? Colors.grey[400]! : Colors.grey[600]!;

    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: primaryColor,
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                'IA',
                style: TextStyle(
                  color: primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              'Family Details',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: Icon(isDark ? Icons.dark_mode : Icons.light_mode,
                color: Colors.white),
            onPressed: () => themeProvider.toggleTheme(),
            tooltip: isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode',
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [
                    const Color(0xFF18181B),
                    const Color(0xFF27272A),
                    const Color(0xFF18181B)
                  ]
                : [
                    const Color(0xFF8B5CF6),
                    const Color(0xFF7C3AED),
                    Colors.white
                  ],
            stops: const [0.0, 0.3, 1.0],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),

                    // Animated Immy Bear Icon
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [primaryColor, secondaryColor],
                              ),
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: primaryColor.withOpacity(0.4),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(30),
                              child: Image.asset(
                                'assets/immy_BrainyBear.png',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Text(
                                      'IB',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 40,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 30),

                    // Welcome Card with Optional Badge
                    Card(
                      elevation: 8,
                      shadowColor: Colors.black.withOpacity(0.2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      color: cardColor,
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 20.0 : 24.0),
                        child: Column(
                          children: [
                            ShaderMask(
                              shaderCallback: (bounds) => LinearGradient(
                                colors: [primaryColor, secondaryColor],
                              ).createShader(bounds),
                              child: Text(
                                'Tell Immy About Your Family!',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Help Immy get to know your family better by sharing some basic information. You can skip this step and add details later.',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 14 : 16,
                                color: hintColor,
                                height: 1.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Family Details Form
                    Card(
                      elevation: 8,
                      shadowColor: Colors.black.withOpacity(0.2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      color: cardColor,
                      child: Padding(
                        padding: EdgeInsets.all(isSmallScreen ? 20.0 : 24.0),
                        child: Column(
                          children: [
                            // User Name Field (Required if filling any field)
                            _buildInputField(
                              controller: _userNameController,
                              label: 'Your Name',
                              icon: Icons.person,
                              hintText: 'Enter your name (Optional)',
                              validator:
                                  null, // Validation handled in _sendFamilyDetails
                              textColor: textColor,
                              hintColor: hintColor,
                              primaryColor: primaryColor,
                            ),

                            const SizedBox(height: 20),

                            // Father Name Field (Optional)
                            _buildInputField(
                              controller: _fatherNameController,
                              label: 'Father\'s Name',
                              icon: Icons.family_restroom,
                              hintText: 'Enter father\'s name (optional)',
                              validator: null, // Made optional
                              textColor: textColor,
                              hintColor: hintColor,
                              primaryColor: primaryColor,
                            ),

                            const SizedBox(height: 20),

                            // Mother Name Field (Optional)
                            _buildInputField(
                              controller: _motherNameController,
                              label: 'Mother\'s Name',
                              icon: Icons.favorite,
                              hintText: 'Enter mother\'s name (optional)',
                              validator: null, // Made optional
                              textColor: textColor,
                              hintColor: hintColor,
                              primaryColor: primaryColor,
                            ),

                            const SizedBox(height: 20),

                            // Siblings Field (Optional)
                            _buildInputField(
                              controller: _siblingsController,
                              label: 'Siblings',
                              icon: Icons.group,
                              hintText: 'Enter siblings names (optional)',
                              validator: null, // Optional field
                              maxLines: 2,
                              textColor: textColor,
                              hintColor: hintColor,
                              primaryColor: primaryColor,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 30),

                    // Button Row with Send and Skip
                    Row(
                      children: [
                        // Skip Button
                        Expanded(
                          flex: 1,
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: primaryColor.withOpacity(0.5),
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: TextButton(
                              onPressed:
                                  _isSendingData ? null : _skipFamilyDetails,
                              style: TextButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.skip_next,
                                    color: primaryColor,
                                    size: 24,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Skip',
                                    style: TextStyle(
                                      color: primaryColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Send Family Details Button
                        Expanded(
                          flex: 2,
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                colors: [primaryColor, secondaryColor],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: primaryColor.withOpacity(0.4),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              onPressed:
                                  _isSendingData ? null : _sendFamilyDetails,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              child: _isSendingData
                                  ? Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const SizedBox(
                                          height: 20,
                                          width: 20,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                            strokeWidth: 2,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Sending...',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    )
                                  : Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.send,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Save & Continue',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required String hintText,
    String? Function(String?)? validator,
    int maxLines = 1,
    required Color textColor,
    required Color hintColor,
    required Color primaryColor,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: primaryColor, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          style: TextStyle(color: textColor),
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: TextStyle(color: hintColor),
            filled: true,
            fillColor: Colors.transparent,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: hintColor.withOpacity(0.3)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: hintColor.withOpacity(0.3)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFDC2626), width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFDC2626), width: 2),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }
}
